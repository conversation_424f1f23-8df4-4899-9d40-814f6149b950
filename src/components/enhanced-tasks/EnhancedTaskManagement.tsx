import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { TaskHeader } from './TaskHeader';
import { EnhancedKanbanView } from './EnhancedKanbanView';
import { EnhancedTableView } from './EnhancedTableView';
import { EnhancedCalendarView } from './EnhancedCalendarView';
import { BulkOperationsToolbar } from './BulkOperationsToolbar';
import { TaskGamificationSystem } from './gamification/TaskGamificationSystem';
import { TaskRecommendationEngine } from './ai/TaskRecommendationEngine';
import { KeyboardNavigationProvider } from './accessibility/KeyboardNavigation';
import { ScreenReaderProvider } from './accessibility/ScreenReaderSupport';
import { PullToRefresh } from './mobile/BottomSheetModal';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  AlertTriangle,
  RefreshCw,
  Wifi,
  WifiOff,
  BarChart3,
} from 'lucide-react';

interface EnhancedTaskManagementProps {
  className?: string;
}

export function EnhancedTaskManagement({ className = '' }: EnhancedTaskManagementProps) {
  const { user } = useSupabaseAuth();
  const store = useEnhancedTodoStore();

  // Destructure with fallbacks
  const {
    fetchTodos,
    fetchSubjects,
    subscribeToUpdates,
    loading = { tasks: false, subjects: false, exams: false, analytics: false },
    error = {},
    viewMode = 'kanban',
    selectedTasks = [],
    clearSelection,
    reset,
  } = store || {};

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showAnalytics, setShowAnalytics] = useState(false);

  // Debug logging
  useEffect(() => {
    console.log('EnhancedTaskManagement render:', {
      user: user?.id,
      loading,
      error,
      viewMode,
      selectedTasksCount: selectedTasks?.length,
      storeExists: !!store
    });
  });

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Initialize data and subscriptions
  useEffect(() => {
    if (!user) {
      reset();
      return;
    }

    const initializeData = async () => {
      try {
        console.log('Initializing enhanced task management for user:', user.id);
        // Fetch all data in parallel
        await Promise.all([
          fetchTodos(user.id).catch(err => {
            console.error('Failed to fetch todos:', err);
            return [];
          }),
          fetchSubjects(user.id).catch(err => {
            console.error('Failed to fetch subjects:', err);
            return [];
          }),
        ]);
        console.log('Enhanced task management initialized successfully');
      } catch (error) {
        console.error('Failed to initialize enhanced task management:', error);
      }
    };

    initializeData();

    // Set up real-time subscription
    let unsubscribe: (() => void) | undefined;
    try {
      unsubscribe = subscribeToUpdates(user.id);
    } catch (error) {
      console.error('Failed to set up real-time subscription:', error);
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [user, fetchTodos, fetchSubjects, subscribeToUpdates, reset]);

  // Handle refresh functionality
  const handleRefresh = async () => {
    if (!user?.id) return;

    setIsRefreshing(true);
    try {
      await Promise.all([
        fetchTodos(user.id),
        fetchSubjects(user.id),
      ]);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle keyboard actions
  const handleKeyboardAction = (action: string, focusedElement?: string) => {
    switch (action) {
      case 'create_task':
        // Trigger task creation modal
        break;
      case 'search':
        // Focus search input
        break;
      case 'toggle_view':
        // Toggle between view modes
        break;
      case 'complete_task':
        if (focusedElement) {
          // Complete the focused task
        }
        break;
      case 'edit_task':
        if (focusedElement) {
          // Edit the focused task
        }
        break;
      case 'delete_task':
        if (focusedElement) {
          // Delete the focused task
        }
        break;
    }
  };

  // Handle retry
  const handleRetry = async () => {
    if (!user) return;

    try {
      await Promise.all([
        fetchTodos(user.id),
        fetchSubjects(user.id),
      ]);
    } catch (error) {
      console.error('Retry failed:', error);
    }
  };

  // Loading state
  if (loading.tasks || loading.subjects || loading.exams) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* Header skeleton */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-8 w-48 bg-gray-200 dark:bg-gray-800" />
              <Skeleton className="h-4 w-32 bg-gray-200 dark:bg-gray-800" />
            </div>
            <div className="flex gap-2">
              <Skeleton className="h-9 w-24 bg-gray-200 dark:bg-gray-800" />
              <Skeleton className="h-9 w-28 bg-gray-200 dark:bg-gray-800" />
            </div>
          </div>

          {/* Search skeleton */}
          <Skeleton className="h-10 w-full bg-gray-200 dark:bg-gray-800" />

          {/* View toggle skeleton */}
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-48 bg-gray-200 dark:bg-gray-800" />
          </div>
        </div>

        {/* Content skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="space-y-4">
              <Skeleton className="h-32 w-full bg-gray-200 dark:bg-gray-800" />
              <Skeleton className="h-24 w-full bg-gray-200 dark:bg-gray-800" />
              <Skeleton className="h-28 w-full bg-gray-200 dark:bg-gray-800" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error.tasks || error.subjects || error.exams) {
    const errorMessage = error.tasks || error.subjects || error.exams;

    return (
      <div className={`space-y-6 ${className}`}>
        <Alert className="border-red-500/50 bg-red-50/80 dark:bg-red-950/20">
          <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
          <AlertDescription className="text-red-700 dark:text-red-300">
            <div className="flex items-center justify-between">
              <span>Failed to load task data: {errorMessage}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRetry}
                className="border-red-500/50 text-red-600 dark:text-red-400 hover:bg-red-500/20"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // No user state
  if (!user) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="text-center space-y-4">
          <AlertTriangle className="h-12 w-12 text-amber-600 dark:text-amber-400 mx-auto" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Authentication Required</h3>
            <p className="text-gray-600 dark:text-gray-400">Please sign in to access task management.</p>
          </div>
        </div>
      </div>
    );
  }

  // Store not available
  if (!store || !fetchTodos || !fetchSubjects) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="text-center space-y-4">
          <AlertTriangle className="h-12 w-12 text-red-600 dark:text-red-400 mx-auto" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Store Initialization Error</h3>
            <p className="text-gray-600 dark:text-gray-400">Task management store is not available. Please refresh the page.</p>
          </div>
        </div>
      </div>
    );
  }

  // Main content animations
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      }
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <ScreenReaderProvider>
      <KeyboardNavigationProvider onAction={handleKeyboardAction}>
        <PullToRefresh onRefresh={handleRefresh}>
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className={`space-y-4 md:space-y-6 ${className} touch-manipulation pb-20`}
          >
            {/* Online/Offline Indicator */}
            <AnimatePresence>
              {!isOnline && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                >
                  <Alert className="border-amber-500/50 bg-amber-50/80 dark:bg-amber-950/20">
                    <WifiOff className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                    <AlertDescription className="text-amber-700 dark:text-amber-300">
                      You're currently offline. Changes will be synced when connection is restored.
                    </AlertDescription>
                  </Alert>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Task Header */}
            <motion.div variants={itemVariants}>
              <TaskHeader />
            </motion.div>

            {/* AI Recommendations */}
            <motion.div variants={itemVariants}>
              <TaskRecommendationEngine />
            </motion.div>

            {/* Main Content Area */}
            <motion.div variants={itemVariants} className="min-h-[600px]">
              <AnimatePresence mode="wait">
                {viewMode === 'kanban' && (
                  <motion.div
                    key="kanban"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <EnhancedKanbanView />
                  </motion.div>
                )}

                {viewMode === 'table' && (
                  <motion.div
                    key="table"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <EnhancedTableView />
                  </motion.div>
                )}

                {viewMode === 'calendar' && (
                  <motion.div
                    key="calendar"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <EnhancedCalendarView />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Bottom Section - Clean and organized */}
            <motion.div variants={itemVariants} className="mt-8 pb-8">
              {/* Gamification System - Compact version */}
              <div className="mb-6">
                <TaskGamificationSystem />
              </div>

              {/* Footer with connection status */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200/50 dark:border-gray-800/50">
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Task management powered by IsotopeAI
                </div>
                
                <div className={`
                  flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium
                  ${isOnline
                    ? 'bg-emerald-500/10 text-emerald-600 dark:text-emerald-400 border border-emerald-500/20'
                    : 'bg-red-500/10 text-red-600 dark:text-red-400 border border-red-500/20'
                  }
                `}>
                  {isOnline ? (
                    <>
                      <Wifi className="h-3 w-3" />
                      Connected
                    </>
                  ) : (
                    <>
                      <WifiOff className="h-3 w-3" />
                      Offline
                    </>
                  )}
                </div>
              </div>
            </motion.div>

            {/* Bulk Operations Toolbar - Fixed position when needed */}
            <AnimatePresence>
              {selectedTasks && selectedTasks.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50"
                >
                  <BulkOperationsToolbar
                    selectedTasks={selectedTasks}
                    onClearSelection={clearSelection}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </PullToRefresh>
      </KeyboardNavigationProvider>
    </ScreenReaderProvider>
  );
}
