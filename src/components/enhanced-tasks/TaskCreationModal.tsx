import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  CalendarIcon,
  Clock,
  Plus,
  X,
  BookOpen,
  Target,
  Tag,
  FileText,
  Settings,
  Save,
  AlertCircle,
} from 'lucide-react';
import { format } from 'date-fns';
import { EnhancedTodoItem } from '@/types/todo';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';

interface TaskCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  editingTask?: EnhancedTodoItem | null;
  parentTaskId?: string;
  columnId?: string;
  initialDueDate?: Date;
}

export function TaskCreationModal({
  isOpen,
  onClose,
  editingTask = null,
  parentTaskId,
  columnId,
  initialDueDate
}: TaskCreationModalProps) {
  const { user } = useSupabaseAuth();
  const { addTask, updateTask, subjects, presetExams, fetchSubjects } = useEnhancedTodoStore();

  // Form state
  const [formData, setFormData] = useState<Partial<EnhancedTodoItem>>({
    title: '',
    description: '',
    priority: 'medium',
    difficultyLevel: 'medium',
    completionPercentage: 0,
    tags: [],
    chapterTags: [],
    timeEstimate: undefined,
    dueDate: initialDueDate ? initialDueDate.getTime() : undefined,
    subjectId: undefined,
    examId: undefined,
    parentId: parentTaskId,
    notes: '',
  });

  const [currentTab, setCurrentTab] = useState('basic');
  const [tagInput, setTagInput] = useState('');
  const [chapterTagInput, setChapterTagInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when editing
  useEffect(() => {
    if (editingTask) {
      setFormData({
        ...editingTask,
        dueDate: editingTask.dueDate || undefined,
      });
    } else {
      setFormData({
        title: '',
        description: '',
        priority: 'medium',
        difficultyLevel: 'medium',
        completionPercentage: 0,
        tags: [],
        chapterTags: [],
        timeEstimate: undefined,
        dueDate: undefined,
        subjectId: undefined,
        examId: undefined,
        parentId: parentTaskId,
        notes: '',
      });
    }
  }, [editingTask, parentTaskId]);

  // Fetch subjects when modal opens
  useEffect(() => {
    if (isOpen && user) {
      fetchSubjects(user.id);
    }
  }, [isOpen, user, fetchSubjects]);

  // Validation
  const validateForm = () => {
    console.log('Validating form with data:', formData);
    const newErrors: Record<string, string> = {};

    // Title validation
    if (!formData.title?.trim()) {
      newErrors.title = 'Title is required';
    } else if (formData.title.length > 500) {
      newErrors.title = 'Title must be less than 500 characters';
    }

    // Description validation
    if (formData.description && formData.description.length > 5000) {
      newErrors.description = 'Description must be less than 5000 characters';
    }

    // Time estimate validation
    if (formData.timeEstimate && formData.timeEstimate < 0) {
      newErrors.timeEstimate = 'Time estimate must be positive';
    }

    // Completion percentage validation
    if (formData.completionPercentage !== undefined &&
        (formData.completionPercentage < 0 || formData.completionPercentage > 100)) {
      newErrors.completionPercentage = 'Completion percentage must be between 0 and 100';
    }

    // Due date validation
    if (formData.dueDate && formData.dueDate < Date.now() - 24 * 60 * 60 * 1000) {
      newErrors.dueDate = 'Due date cannot be more than 1 day in the past';
    }

    // Notes validation
    if (formData.notes && formData.notes.length > 10000) {
      newErrors.notes = 'Notes must be less than 10000 characters';
    }

    console.log('Validation errors:', newErrors);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e?: React.FormEvent) => {
    console.log('handleSubmit entered');
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    console.log('Default prevented and propagation stopped');

    console.log('Form submission started', { formData, columnId });
    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const taskData: Partial<EnhancedTodoItem> = {
        ...formData,
        createdBy: user?.id || '',
        columnId: columnId || formData.columnId || 'column-1',
        updatedAt: Date.now(),
        // Ensure arrays are properly initialized
        tags: formData.tags || [],
        chapterTags: formData.chapterTags || [],
        // Set defaults for required fields
        priority: formData.priority || 'medium',
        difficultyLevel: formData.difficultyLevel || 'medium',
        completionPercentage: formData.completionPercentage || 0,
      };

      console.log('Submitting task data:', taskData);

      if (editingTask) {
        console.log('Updating existing task:', editingTask.id);
        await updateTask(editingTask.id, taskData);
      } else {
        console.log('Creating new task');
        await addTask(taskData);
      }

      console.log('Task saved successfully');

      // Reset form on successful creation
      if (!editingTask) {
        setFormData({
          title: '',
          description: '',
          priority: 'medium',
          difficultyLevel: 'medium',
          completionPercentage: 0,
          tags: [],
          chapterTags: [],
          timeEstimate: undefined,
          dueDate: initialDueDate ? initialDueDate.getTime() : undefined,
          subjectId: undefined,
          examId: undefined,
          parentId: parentTaskId,
          notes: '',
        });
        setTagInput('');
        setChapterTagInput('');
      }

      onClose();
    } catch (error) {
      console.error('Error saving task:', error);
      setErrors({
        submit: error instanceof Error ? error.message : 'Failed to save task. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle tag addition
  const addTag = (type: 'tags' | 'chapterTags', e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    const input = type === 'tags' ? tagInput : chapterTagInput;
    if (!input.trim()) return;

    const currentTags = formData[type] || [];
    if (!currentTags.includes(input.trim())) {
      setFormData(prev => ({
        ...prev,
        [type]: [...currentTags, input.trim()],
      }));
    }

    if (type === 'tags') {
      setTagInput('');
    } else {
      setChapterTagInput('');
    }
  };

  // Handle tag removal
  const removeTag = (type: 'tags' | 'chapterTags', tagToRemove: string) => {
    const currentTags = formData[type] || [];
    setFormData(prev => ({
      ...prev,
      [type]: currentTags.filter(tag => tag !== tagToRemove),
    }));
  };

  // Handle key press for tag inputs
  const handleTagKeyPress = (e: React.KeyboardEvent, type: 'tags' | 'chapterTags') => {
    if (e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation();
      addTag(type);
    }
  };

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 },
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white/95 dark:bg-[#030303]/95 backdrop-blur-md border-gray-200 dark:border-gray-800 text-gray-900 dark:text-white">
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          transition={{ duration: 0.3 }}
        >
          <DialogHeader>
            <DialogTitle className="font-onest text-xl text-gray-900 dark:text-white flex items-center gap-2">
              {editingTask ? (
                <>
                  <Settings className="h-5 w-5 text-violet-400" />
                  Edit Task
                </>
              ) : (
                <>
                  <Plus className="h-5 w-5 text-emerald-400" />
                  Create New Task
                </>
              )}
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleSubmit(e);
          }} className="mt-6">
            <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-4 bg-gray-100 dark:bg-gray-800/50">
              <TabsTrigger value="basic" className="data-[state=active]:bg-violet-500/20 data-[state=active]:text-violet-600 dark:data-[state=active]:text-violet-300">
                <FileText className="h-4 w-4 mr-1" />
                Basic
              </TabsTrigger>
              <TabsTrigger value="details" className="data-[state=active]:bg-violet-500/20 data-[state=active]:text-violet-600 dark:data-[state=active]:text-violet-300">
                <Settings className="h-4 w-4 mr-1" />
                Details
              </TabsTrigger>
              <TabsTrigger value="organization" className="data-[state=active]:bg-violet-500/20 data-[state=active]:text-violet-600 dark:data-[state=active]:text-violet-300">
                <BookOpen className="h-4 w-4 mr-1" />
                Organization
              </TabsTrigger>
              <TabsTrigger value="tags" className="data-[state=active]:bg-violet-500/20 data-[state=active]:text-violet-600 dark:data-[state=active]:text-violet-300">
                <Tag className="h-4 w-4 mr-1" />
                Tags
              </TabsTrigger>
            </TabsList>

            {/* Basic Information Tab */}
            <TabsContent value="basic" className="space-y-4 mt-6">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-gray-700 dark:text-gray-300">
                  Task Title *
                </Label>
                <Input
                  id="title"
                  value={formData.title || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter task title..."
                  className="bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-400 focus:border-violet-500"
                />
                {errors.title && (
                  <p className="text-red-400 text-sm flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.title}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="text-gray-300">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={formData.description || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe the task..."
                  rows={4}
                  className="bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-400 focus:border-violet-500 resize-none"
                />
                {errors.description && (
                  <p className="text-red-400 text-sm flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.description}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-gray-300">Priority</Label>
                  <Select
                    value={formData.priority}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value as any }))}
                  >
                    <SelectTrigger className="bg-gray-800/50 border-gray-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="low" className="text-green-400">Low Priority</SelectItem>
                      <SelectItem value="medium" className="text-amber-400">Medium Priority</SelectItem>
                      <SelectItem value="high" className="text-red-400">High Priority</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-gray-300">Difficulty</Label>
                  <Select
                    value={formData.difficultyLevel}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, difficultyLevel: value as any }))}
                  >
                    <SelectTrigger className="bg-gray-800/50 border-gray-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="easy" className="text-emerald-400">Easy</SelectItem>
                      <SelectItem value="medium" className="text-blue-400">Medium</SelectItem>
                      <SelectItem value="hard" className="text-purple-400">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            {/* Details Tab */}
            <TabsContent value="details" className="space-y-4 mt-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-gray-300">Due Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        type="button"
                        variant="outline"
                        className="w-full justify-start text-left font-normal bg-gray-800/50 border-gray-700 text-white hover:bg-gray-700/50"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.dueDate ? format(formData.dueDate, 'PPP') : 'Pick a date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-gray-800 border-gray-700">
                      <Calendar
                        mode="single"
                        selected={formData.dueDate ? new Date(formData.dueDate) : undefined}
                        onSelect={(date) => setFormData(prev => ({ ...prev, dueDate: date?.getTime() }))}
                        initialFocus
                        className="text-white"
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timeEstimate" className="text-gray-300">
                    Time Estimate (minutes)
                  </Label>
                  <Input
                    id="timeEstimate"
                    type="number"
                    value={formData.timeEstimate || ''}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      timeEstimate: e.target.value ? parseInt(e.target.value) : undefined 
                    }))}
                    placeholder="60"
                    className="bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-400 focus:border-violet-500"
                  />
                  {errors.timeEstimate && (
                    <p className="text-red-400 text-sm flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.timeEstimate}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="completion" className="text-gray-300">
                  Completion Percentage
                </Label>
                <div className="flex items-center gap-3">
                  <Input
                    id="completion"
                    type="number"
                    min="0"
                    max="100"
                    value={formData.completionPercentage || 0}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      completionPercentage: parseInt(e.target.value) || 0 
                    }))}
                    className="bg-gray-800/50 border-gray-700 text-white focus:border-violet-500"
                  />
                  <span className="text-gray-400">%</span>
                </div>
                {errors.completionPercentage && (
                  <p className="text-red-400 text-sm flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.completionPercentage}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes" className="text-gray-300">
                  Notes
                </Label>
                <Textarea
                  id="notes"
                  value={formData.notes || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Additional notes..."
                  rows={3}
                  className="bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-400 focus:border-violet-500 resize-none"
                />
              </div>
            </TabsContent>

            {/* Organization Tab */}
            <TabsContent value="organization" className="space-y-4 mt-6">
              <div className="space-y-2">
                <Label className="text-gray-300 flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Subject
                </Label>
                <Select
                  value={formData.subjectId || ''}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, subjectId: value || undefined }))}
                >
                  <SelectTrigger className="bg-gray-800/50 border-gray-700 text-white">
                    <SelectValue placeholder="Select a subject..." />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700">
                    <SelectItem value="">No Subject</SelectItem>
                    {Object.values(subjects).map((subject) => (
                      <SelectItem key={subject.id} value={subject.id}>
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: subject.color }}
                          />
                          {subject.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-gray-300 flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Exam
                </Label>
                <Select
                  value={formData.examId || ''}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, examId: value as any || undefined }))}
                >
                  <SelectTrigger className="bg-gray-800/50 border-gray-700 text-white">
                    <SelectValue placeholder="Select an exam..." />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700">
                    <SelectItem value="">No Exam</SelectItem>
                    {Object.values(presetExams).map((exam) => (
                      <SelectItem key={exam.id} value={exam.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{exam.name}</span>
                          <span className="text-xs text-gray-400 ml-2">
                            {exam.category}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            {/* Tags Tab */}
            <TabsContent value="tags" className="space-y-4 mt-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-gray-300">General Tags</Label>
                  <div className="flex gap-2">
                    <Input
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyPress={(e) => handleTagKeyPress(e, 'tags')}
                      placeholder="Add a tag..."
                      className="bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-400 focus:border-violet-500"
                    />
                    <Button
                      type="button"
                      onClick={(e) => addTag('tags', e)}
                      className="bg-violet-500 hover:bg-violet-600"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags?.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="bg-violet-500/20 text-violet-300 hover:bg-violet-500/30"
                      >
                        #{tag}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0 ml-1 hover:bg-violet-500/40"
                          onClick={() => removeTag('tags', tag)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>

                <Separator className="bg-gray-700" />

                <div className="space-y-2">
                  <Label className="text-gray-300">Chapter Tags</Label>
                  <div className="flex gap-2">
                    <Input
                      value={chapterTagInput}
                      onChange={(e) => setChapterTagInput(e.target.value)}
                      onKeyPress={(e) => handleTagKeyPress(e, 'chapterTags')}
                      placeholder="Add a chapter tag..."
                      className="bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-400 focus:border-violet-500"
                    />
                    <Button
                      type="button"
                      onClick={(e) => addTag('chapterTags', e)}
                      className="bg-emerald-500 hover:bg-emerald-600"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.chapterTags?.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="bg-emerald-500/20 text-emerald-300 hover:bg-emerald-500/30"
                      >
                        📖 {tag}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0 ml-1 hover:bg-emerald-500/40"
                          onClick={() => removeTag('chapterTags', tag)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {errors.submit && (
            <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
              <p className="text-red-400 text-sm flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                {errors.submit}
              </p>
            </div>
          )}
        </form>

        <DialogFooter className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-800">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800/50"
            >
              Cancel
            </Button>
            <Button
              type="button"
              disabled={isSubmitting}
              className="bg-violet-500 hover:bg-violet-600 text-white"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleSubmit(e);
              }}
            >
              {isSubmitting ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="mr-2"
                  >
                    <Settings className="h-4 w-4" />
                  </motion.div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {editingTask ? 'Update Task' : 'Create Task'}
                </>
              )}
            </Button>
          </DialogFooter>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
