import { create } from 'zustand';

import {
  getEnhancedTodos,
  createEnhancedTodo,
  updateEnhancedTodo,
  deleteEnhancedTodo,
  subscribeToEnhancedTodos,
  getUserSubjects,
  getTodosWithHierarchy,
  bulkUpdateEnhancedTodos,
  getTaskAnalytics,
  searchTasks,
  exportTasks,
  importTasks,
} from '../utils/enhancedSupabase';
import {
  EnhancedTodoState,
  EnhancedTodoItem,
  EnhancedTodoBoard,
  TodoColumn,
  Subject,
  PresetExam,
  PresetExamType,
  FilterState,
  SearchState,
  AnalyticsData,
  LoadingState,
  ErrorState,
  BulkOperation,
  EnhancedDragItem,
} from '../types/todo';
import { PRESET_EXAMS, getPresetExam } from '../data/presetExams';

// Initial enhanced board state
const initialEnhancedBoard: EnhancedTodoBoard = {
  tasks: {},
  columns: {
    'column-1': {
      id: 'column-1',
      title: 'Todo',
      taskIds: [],
      color: '#6366f1', // Indigo
    },
    'column-2': {
      id: 'column-2',
      title: 'In Progress',
      taskIds: [],
      color: '#f59e0b', // Amber
    },
    'column-3': {
      id: 'column-3',
      title: 'Done',
      taskIds: [],
      color: '#10b981', // Emerald
    },
  },
  columnOrder: ['column-1', 'column-2', 'column-3'],
};

// Initial filter state
const initialFilterState: FilterState = {
  subjects: [],
  priorities: [],
  statuses: [],
  dateRange: {},
  tags: [],
  examIds: [],
  showOverdue: false,
  showCompleted: true,
  difficultyLevels: [],
};

// Initial search state
const initialSearchState: SearchState = {
  query: '',
  filters: initialFilterState,
  savedFilters: [],
  activeFilterId: undefined,
};

// Initial analytics data
const initialAnalyticsData: AnalyticsData = {
  completionRates: {
    overall: 0,
    bySubject: {},
    byPriority: {},
    byDifficulty: {},
  },
  taskVelocity: {
    daily: 0,
    weekly: 0,
    monthly: 0,
  },
  studyPatterns: {
    productiveHours: [],
    averageTaskDuration: 0,
    completionStreak: 0,
  },
  subjectDistribution: {},
  upcomingDeadlines: [],
};

// Initial loading state
const initialLoadingState: LoadingState = {
  tasks: false,
  subjects: false,
  exams: false,
  analytics: false,
};

// Initial error state
const initialErrorState: ErrorState = {};

// Initial enhanced state
const initialEnhancedState: EnhancedTodoState = {
  board: initialEnhancedBoard,
  subjects: {},
  presetExams: PRESET_EXAMS,
  search: initialSearchState,
  selectedTasks: [],
  viewMode: 'kanban',
  analytics: initialAnalyticsData,
  loading: initialLoadingState,
  error: initialErrorState,
};

// Enhanced todo store with advanced features
export const useEnhancedTodoStore = create<
  EnhancedTodoState & {
    // Core CRUD operations
    fetchTodos: (userId: string) => Promise<void>;
    addTask: (task: Partial<EnhancedTodoItem>) => Promise<void>;
    updateTask: (taskId: string, updates: Partial<EnhancedTodoItem>) => Promise<void>;
    deleteTask: (taskId: string, deleteSubtasks?: boolean) => Promise<void>;
    moveTask: (source: EnhancedDragItem, destination: EnhancedDragItem) => Promise<void>;

    // Hierarchical task operations
    addSubtask: (parentId: string, subtask: Partial<EnhancedTodoItem>) => Promise<void>;
    promoteTask: (taskId: string) => Promise<void>;
    demoteTask: (taskId: string, newParentId: string) => Promise<void>;

    // Column management operations
    addColumn: (title: string, color?: string) => Promise<void>;
    updateColumn: (columnId: string, title: string, color?: string) => Promise<void>;
    deleteColumn: (columnId: string) => Promise<void>;

    // Subject integration
    fetchSubjects: (userId: string) => Promise<void>;
    linkTaskToSubject: (taskId: string, subjectId: string) => Promise<void>;
    linkTaskToExam: (taskId: string, examId: PresetExamType) => Promise<void>;

    // Search and filtering
    setSearchQuery: (query: string) => void;
    setFilters: (filters: Partial<FilterState>) => void;
    clearFilters: () => void;
    saveFilter: (name: string, filters: FilterState) => void;
    loadFilter: (filterId: string) => void;
    deleteFilter: (filterId: string) => void;

    // Bulk operations
    selectTask: (taskId: string) => void;
    selectAllTasks: (taskIds: string[]) => void;
    clearSelection: () => void;
    bulkOperation: (operation: BulkOperation) => Promise<void>;
    bulkUpdateTasks: (updates: Array<{ id: string; updates: Partial<EnhancedTodoItem> }>, onProgress?: (progress: number) => void) => Promise<void>;

    // View management
    setViewMode: (mode: 'kanban' | 'table' | 'calendar') => void;

    // Analytics
    calculateAnalytics: () => void;
    getTasksBySubject: (subjectId: string) => EnhancedTodoItem[];
    getOverdueTasks: () => EnhancedTodoItem[];
    getUpcomingDeadlines: (days: number) => EnhancedTodoItem[];

    // Utility functions
    getFilteredTasks: () => EnhancedTodoItem[];
    getTaskHierarchy: (taskId: string) => EnhancedTodoItem[];
    calculateTaskProgress: (taskId: string) => number;

    // Real-time subscription
    subscribeToUpdates: (userId: string) => () => void;

    // Reset and cleanup
    reset: () => void;
  }
>()(
    (set, get) => ({
      ...initialEnhancedState,

      // Core CRUD operations
      fetchTodos: async (userId: string) => {
        console.log('Enhanced store: fetchTodos called for user:', userId);
        set((state) => ({
          loading: { ...state.loading, tasks: true },
          error: { ...state.error, tasks: undefined },
        }));

        try {
          // Fetch todos with hierarchy and computed fields
          console.log('Enhanced store: calling getTodosWithHierarchy...');
          const todos = await getTodosWithHierarchy(userId);
          console.log('Enhanced store: received todos:', todos.length, 'items');

          const tasks: Record<string, EnhancedTodoItem> = {};
          const columns = JSON.parse(JSON.stringify(get().board.columns));

          // Reset taskIds in all columns
          Object.keys(columns).forEach(columnId => {
            columns[columnId].taskIds = [];
          });

          todos.forEach((todo) => {
            console.log('Enhanced store: processing todo:', todo.id, todo.title);
            tasks[todo.id] = todo;

            // Find which column this task belongs to
            const columnId = todo.columnId || 'column-1';
            if (columns[columnId]) {
              columns[columnId].taskIds.push(todo.id);
            } else {
              // If column doesn't exist, move to Todo
              columns['column-1'].taskIds.push(todo.id);
            }
          });

          console.log('Enhanced store: final tasks object:', Object.keys(tasks).length, 'tasks');
          console.log('Enhanced store: final columns:', columns);

          set((state) => ({
            board: {
              ...state.board,
              tasks,
              columns,
            },
            loading: { ...state.loading, tasks: false },
          }));

          // Calculate analytics after loading tasks
          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error fetching enhanced todos:', error);
          set((state) => ({
            loading: { ...state.loading, tasks: false },
            error: { ...state.error, tasks: error.message },
          }));
        }
      },

      addTask: async (taskData: Partial<EnhancedTodoItem>) => {
        console.log('Enhanced store: addTask called with:', taskData);
        try {
          const taskId = `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          const now = Date.now();

          // Create a minimal task data object with only basic fields for now
          const newTaskData: Partial<EnhancedTodoItem> = {
            id: taskId,
            title: taskData.title || 'New Task',
            description: taskData.description || '',
            priority: taskData.priority || 'medium',
            createdAt: now,
            updatedAt: now,
            columnId: taskData.columnId || 'column-1',
            // Only include enhanced fields if they have values
            ...(taskData.difficultyLevel && { difficultyLevel: taskData.difficultyLevel }),
            ...(taskData.completionPercentage !== undefined && { completionPercentage: taskData.completionPercentage }),
            ...(taskData.tags && taskData.tags.length > 0 && { tags: taskData.tags }),
            ...(taskData.chapterTags && taskData.chapterTags.length > 0 && { chapterTags: taskData.chapterTags }),
            ...(taskData.timeEstimate && { timeEstimate: taskData.timeEstimate }),
            ...(taskData.dueDate && { dueDate: taskData.dueDate }),
            ...(taskData.subjectId && { subjectId: taskData.subjectId }),
            ...(taskData.examId && { examId: taskData.examId }),
            ...(taskData.notes && { notes: taskData.notes }),
            ...(taskData.parentId && { parentId: taskData.parentId }),
          };

          console.log('Enhanced store: calling createEnhancedTodo with:', newTaskData);
          const createdTask = await createEnhancedTodo(newTaskData);
          console.log('Enhanced store: created task:', createdTask);

          // Update local state immediately for better UX
          set((state) => {
            const columnId = createdTask.columnId || 'column-1';
            const newTasks = { ...state.board.tasks };
            const newColumns = { ...state.board.columns };

            newTasks[createdTask.id] = createdTask;
            
            if (newColumns[columnId]) {
              newColumns[columnId] = {
                ...newColumns[columnId],
                taskIds: [...newColumns[columnId].taskIds, createdTask.id],
              };
            }

            return {
              board: {
                ...state.board,
                tasks: newTasks,
                columns: newColumns,
              },
            };
          });

          // Recalculate analytics
          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error adding enhanced task:', error);

          // Provide user-friendly error messages
          let errorMessage = 'Failed to create task';
          if (error.message?.includes('Validation failed')) {
            errorMessage = error.message;
          } else if (error.message?.includes('not authenticated')) {
            errorMessage = 'Please sign in to create tasks';
          } else if (error.message?.includes('network')) {
            errorMessage = 'Network error. Please check your connection and try again.';
          }

          set((state) => ({
            error: { ...state.error, tasks: errorMessage },
          }));
          throw new Error(errorMessage);
        }
      },

      updateTask: async (taskId: string, updates: Partial<EnhancedTodoItem>) => {
        try {
          const currentTask = get().board.tasks[taskId];
          const updatedTask = await updateEnhancedTodo(taskId, updates);

          // Check if task was completed
          const wasCompleted = currentTask?.completionPercentage === 100;
          const isNowCompleted = updatedTask.completionPercentage === 100;
          const justCompleted = !wasCompleted && isNowCompleted;

          // Update local state immediately
          set((state) => {
            const newTasks = { ...state.board.tasks };
            newTasks[taskId] = updatedTask;

            return {
              board: {
                ...state.board,
                tasks: newTasks,
              },
            };
          });

          // Trigger gamification if task was just completed
          if (justCompleted) {
            // Dispatch custom event for gamification system
            const event = new CustomEvent('taskCompleted', {
              detail: { task: updatedTask }
            });
            window.dispatchEvent(event);
          }

          // Recalculate analytics
          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error updating enhanced task:', error);

          // Provide user-friendly error messages
          let errorMessage = 'Failed to update task';
          if (error.message?.includes('Validation failed')) {
            errorMessage = error.message;
          } else if (error.message?.includes('not authenticated')) {
            errorMessage = 'Please sign in to update tasks';
          } else if (error.message?.includes('network')) {
            errorMessage = 'Network error. Please check your connection and try again.';
          }

          set((state) => ({
            error: { ...state.error, tasks: errorMessage },
          }));
          throw new Error(errorMessage);
        }
      },

      deleteTask: async (taskId: string, deleteSubtasks: boolean = false) => {
        try {
          await deleteEnhancedTodo(taskId, deleteSubtasks);

          // Update local state immediately
          set((state) => {
            const newTasks = { ...state.board.tasks };
            const newColumns = { ...state.board.columns };

            // Remove task
            delete newTasks[taskId];

            // Remove from columns
            Object.keys(newColumns).forEach(columnId => {
              newColumns[columnId] = {
                ...newColumns[columnId],
                taskIds: newColumns[columnId].taskIds.filter(id => id !== taskId),
              };
            });

            // If deleteSubtasks is false, handle orphaned subtasks
            if (!deleteSubtasks) {
              Object.values(newTasks).forEach(task => {
                if (task.parentId === taskId) {
                  // Move subtasks to the deleted task's parent (or make them root tasks)
                  const deletedTask = state.board.tasks[taskId];
                  newTasks[task.id] = {
                    ...task,
                    parentId: deletedTask?.parentId || undefined,
                  };
                }
              });
            } else {
              // Remove all subtasks recursively
              const removeSubtasks = (parentId: string) => {
                Object.values(newTasks).forEach(task => {
                  if (task.parentId === parentId) {
                    removeSubtasks(task.id);
                    delete newTasks[task.id];
                    
                    // Remove from columns
                    Object.keys(newColumns).forEach(columnId => {
                      newColumns[columnId] = {
                        ...newColumns[columnId],
                        taskIds: newColumns[columnId].taskIds.filter(id => id !== task.id),
                      };
                    });
                  }
                });
              };
              removeSubtasks(taskId);
            }

            return {
              board: {
                ...state.board,
                tasks: newTasks,
                columns: newColumns,
              },
            };
          });

          // Recalculate analytics
          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error deleting enhanced task:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      moveTask: async (source: EnhancedDragItem, destination: EnhancedDragItem) => {
        try {
          const { board } = get();
          const sourceColumn = board.columns[source.droppableId];
          const destColumn = board.columns[destination.droppableId];

          if (!sourceColumn || !destColumn) {
            throw new Error('Invalid source or destination column');
          }

          const taskId = sourceColumn.taskIds[source.index];
          const task = board.tasks[taskId];

          if (!task) {
            throw new Error('Task not found');
          }

          // Update task's column in database
          await updateEnhancedTodo(taskId, {
            columnId: destination.droppableId,
            updatedAt: Date.now(),
          });

          // Update local state immediately for better UX
          set((state) => {
            const newColumns = { ...state.board.columns };
            const newTasks = { ...state.board.tasks };

            // Remove from source column
            const newSourceTaskIds = [...sourceColumn.taskIds];
            newSourceTaskIds.splice(source.index, 1);
            newColumns[source.droppableId] = {
              ...sourceColumn,
              taskIds: newSourceTaskIds,
            };

            // Add to destination column
            const newDestTaskIds = [...destColumn.taskIds];
            newDestTaskIds.splice(destination.index, 0, taskId);
            newColumns[destination.droppableId] = {
              ...destColumn,
              taskIds: newDestTaskIds,
            };

            // Update task's column reference
            newTasks[taskId] = {
              ...task,
              columnId: destination.droppableId,
              updatedAt: Date.now(),
            };

            return {
              board: {
                ...state.board,
                tasks: newTasks,
                columns: newColumns,
              },
            };
          });

          console.log('Task moved successfully:', taskId, 'from', source.droppableId, 'to', destination.droppableId);
        } catch (error: any) {
          console.error('Error moving task:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      addSubtask: async (parentId: string, subtask: Partial<EnhancedTodoItem>) => {
        const subtaskData = {
          ...subtask,
          parentId,
        };
        await get().addTask(subtaskData);
      },

      promoteTask: async (taskId: string) => {
        const task = get().board.tasks[taskId];
        if (task && task.parentId) {
          const parent = get().board.tasks[task.parentId];
          await get().updateTask(taskId, {
            parentId: parent?.parentId || undefined,
          });
        }
      },

      demoteTask: async (taskId: string, newParentId: string) => {
        await get().updateTask(taskId, { parentId: newParentId });
      },

      // Subject and exam integration
      fetchSubjects: async (userId: string) => {
        set((state) => ({
          loading: { ...state.loading, subjects: true },
          error: { ...state.error, subjects: undefined },
        }));

        try {
          const subjects = await getUserSubjects(userId);
          const subjectsMap = subjects.reduce((acc, subject) => {
            acc[subject.id] = subject;
            return acc;
          }, {} as Record<string, Subject>);

          set((state) => ({
            subjects: subjectsMap,
            loading: { ...state.loading, subjects: false },
          }));
        } catch (error: any) {
          console.error('Error fetching subjects:', error);
          set((state) => ({
            loading: { ...state.loading, subjects: false },
            error: { ...state.error, subjects: error.message },
          }));
        }
      },

      linkTaskToSubject: async (taskId: string, subjectId: string) => {
        await get().updateTask(taskId, { subjectId });
      },

      linkTaskToExam: async (taskId: string, examId: PresetExamType) => {
        await get().updateTask(taskId, { examId });
      },

      setSearchQuery: (query: string) => {
        set((state) => ({
          search: {
            ...state.search,
            query,
          },
        }));
      },

      setFilters: (filters: Partial<FilterState>) => {
        set((state) => ({
          search: {
            ...state.search,
            filters: {
              ...state.search.filters,
              ...filters,
            },
          },
        }));
      },

      clearFilters: () => {
        set((state) => ({
          search: {
            ...state.search,
            filters: initialFilterState,
          },
        }));
      },

      saveFilter: (name: string, filters: FilterState) => {
        const filterId = `filter_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const savedFilter = {
          id: filterId,
          name,
          filters,
          createdAt: Date.now(),
        };

        set((state) => ({
          search: {
            ...state.search,
            savedFilters: [...state.search.savedFilters, savedFilter],
          },
        }));
      },

      loadFilter: (filterId: string) => {
        const state = get();
        const savedFilter = state.search.savedFilters.find(f => f.id === filterId);
        if (savedFilter) {
          set({
            search: {
              ...state.search,
              filters: savedFilter.filters,
              activeFilterId: filterId,
            },
          });
        }
      },

      deleteFilter: (filterId: string) => {
        set((state) => ({
          search: {
            ...state.search,
            savedFilters: state.search.savedFilters.filter(f => f.id !== filterId),
            activeFilterId: state.search.activeFilterId === filterId ? undefined : state.search.activeFilterId,
          },
        }));
      },

      selectTask: (taskId: string) => {
        set((state) => ({
          selectedTasks: state.selectedTasks.includes(taskId)
            ? state.selectedTasks.filter(id => id !== taskId)
            : [...state.selectedTasks, taskId],
        }));
      },

      selectAllTasks: (taskIds?: string[]) => {
        if (taskIds) {
          set({ selectedTasks: taskIds });
        } else {
          const filteredTasks = get().getFilteredTasks();
          set({ selectedTasks: filteredTasks.map(task => task.id) });
        }
      },

      clearSelection: () => {
        set({ selectedTasks: [] });
      },

      bulkOperation: async (operation: BulkOperation) => {
        try {
          const { type, taskIds, value } = operation;
          
          let updates: Partial<EnhancedTodoItem> = {};
          
          switch (type) {
            case 'complete':
              updates = { completionPercentage: 100 };
              break;
            case 'priority':
              updates = { priority: value };
              break;
            case 'subject':
              updates = { subjectId: value };
              break;
            case 'status':
              updates = { columnId: value };
              break;
            case 'exam': // Added exam type for bulk operations
              updates = { examId: value };
              break;
            case 'delete':
              // Handle delete separately
              for (const taskId of taskIds) {
                await get().deleteTask(taskId);
              }
              get().clearSelection();
              return;
          }

          if (Object.keys(updates).length > 0) {
            await bulkUpdateEnhancedTodos(taskIds, updates);
            
            // Update local state
            set((state) => {
              const newTasks = { ...state.board.tasks };
              taskIds.forEach(taskId => {
                if (newTasks[taskId]) {
                  newTasks[taskId] = { ...newTasks[taskId], ...updates };
                }
              });
              
              return {
                board: {
                  ...state.board,
                  tasks: newTasks,
                },
              };
            });
          }

          get().clearSelection();
          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error performing bulk operation:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      bulkUpdateTasks: async (updates: Array<{ id: string; updates: Partial<EnhancedTodoItem> }>, onProgress?: (progress: number) => void) => {
        try {
          set((state) => ({
            loading: { ...state.loading, tasks: true },
            error: { ...state.error, tasks: null },
          }));

          for (let i = 0; i < updates.length; i++) {
            const { id, updates: taskUpdates } = updates[i];
            await updateEnhancedTodo(id, taskUpdates);

            // Update progress
            if (onProgress) {
              onProgress(((i + 1) / updates.length) * 100);
            }

            // Update local state
            set((state) => ({
              board: {
                ...state.board,
                tasks: {
                  ...state.board.tasks,
                  [id]: {
                    ...state.board.tasks[id],
                    ...taskUpdates,
                    updatedAt: Date.now(),
                  },
                },
              },
            }));
          }

          set((state) => ({
            loading: { ...state.loading, tasks: false },
          }));

          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error performing bulk update:', error);
          set((state) => ({
            loading: { ...state.loading, tasks: false },
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      setViewMode: (mode: 'kanban' | 'table' | 'calendar') => {
        set({ viewMode: mode });
      },

      calculateAnalytics: () => {
        const state = get();
        const tasks = Object.values(state.board.tasks);
        const subjects = Object.values(state.subjects);

        // Calculate completion rates
        const completedTasks = tasks.filter(task => task.completionPercentage === 100);
        const overallCompletionRate = tasks.length > 0 ? (completedTasks.length / tasks.length) * 100 : 0;

        // Calculate by subject
        const bySubject: Record<string, number> = {};
        subjects.forEach(subject => {
          const subjectTasks = tasks.filter(task => task.subjectId === subject.id);
          const subjectCompleted = subjectTasks.filter(task => task.completionPercentage === 100);
          bySubject[subject.id] = subjectTasks.length > 0 ? (subjectCompleted.length / subjectTasks.length) * 100 : 0;
        });

        // Calculate by priority
        const byPriority: Record<string, number> = {};
        ['low', 'medium', 'high'].forEach(priority => {
          const priorityTasks = tasks.filter(task => task.priority === priority);
          const priorityCompleted = priorityTasks.filter(task => task.completionPercentage === 100);
          byPriority[priority] = priorityTasks.length > 0 ? (priorityCompleted.length / priorityTasks.length) * 100 : 0;
        });

        // Calculate by difficulty
        const byDifficulty: Record<string, number> = {};
        ['easy', 'medium', 'hard'].forEach(difficulty => {
          const difficultyTasks = tasks.filter(task => task.difficultyLevel === difficulty);
          const difficultyCompleted = difficultyTasks.filter(task => task.completionPercentage === 100);
          byDifficulty[difficulty] = difficultyTasks.length > 0 ? (difficultyCompleted.length / difficultyTasks.length) * 100 : 0;
        });

        // Calculate upcoming deadlines
        const now = Date.now();
        const upcomingDeadlines = tasks
          .filter(task => task.dueDate && task.dueDate > now && task.completionPercentage < 100)
          .sort((a, b) => (a.dueDate || 0) - (b.dueDate || 0))
          .slice(0, 10);

        // Subject distribution
        const subjectDistribution: Record<string, number> = {};
        subjects.forEach(subject => {
          subjectDistribution[subject.id] = tasks.filter(task => task.subjectId === subject.id).length;
        });

        const analytics: AnalyticsData = {
          completionRates: {
            overall: overallCompletionRate,
            bySubject,
            byPriority,
            byDifficulty,
          },
          taskVelocity: {
            daily: 0, // Will be calculated based on completion history
            weekly: 0,
            monthly: 0,
          },
          studyPatterns: {
            productiveHours: [],
            averageTaskDuration: 0,
            completionStreak: 0,
          },
          subjectDistribution,
          upcomingDeadlines,
        };

        set({ analytics });
      },

      getTasksBySubject: (subjectId: string) => {
        const tasks = Object.values(get().board.tasks);
        return tasks.filter(task => task.subjectId === subjectId);
      },

      getOverdueTasks: () => {
        const tasks = Object.values(get().board.tasks);
        const now = Date.now();
        return tasks.filter(task => 
          task.dueDate && 
          task.dueDate < now && 
          task.completionPercentage < 100
        );
      },

      getUpcomingDeadlines: (days: number) => {
        const tasks = Object.values(get().board.tasks);
        const now = Date.now();
        const futureTime = now + (days * 24 * 60 * 60 * 1000);
        
        return tasks
          .filter(task => 
            task.dueDate && 
            task.dueDate > now && 
            task.dueDate <= futureTime &&
            task.completionPercentage < 100
          )
          .sort((a, b) => (a.dueDate || 0) - (b.dueDate || 0));
      },

      getFilteredTasks: () => {
        const state = get();
        const tasks = Object.values(state.board.tasks);
        const { query, filters } = state.search;

        let filteredTasks = tasks;

        // Apply text search
        if (query.trim()) {
          const searchTerm = query.toLowerCase();
          filteredTasks = filteredTasks.filter(task =>
            task.title.toLowerCase().includes(searchTerm) ||
            task.description.toLowerCase().includes(searchTerm) ||
            task.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
            task.chapterTags.some(tag => tag.toLowerCase().includes(searchTerm))
          );
        }

        // Apply filters
        if (filters.subjects.length > 0) {
          filteredTasks = filteredTasks.filter(task => 
            task.subjectId && filters.subjects.includes(task.subjectId)
          );
        }

        if (filters.priorities.length > 0) {
          filteredTasks = filteredTasks.filter(task => 
            filters.priorities.includes(task.priority)
          );
        }

        if (filters.difficultyLevels.length > 0) {
          filteredTasks = filteredTasks.filter(task => 
            filters.difficultyLevels.includes(task.difficultyLevel)
          );
        }

        if (filters.tags.length > 0) {
          filteredTasks = filteredTasks.filter(task =>
            filters.tags.some(tag => task.tags.includes(tag) || task.chapterTags.includes(tag))
          );
        }

        if (filters.examIds.length > 0) {
          filteredTasks = filteredTasks.filter(task => 
            task.examId && filters.examIds.includes(task.examId)
          );
        }

        if (filters.showOverdue) {
          const now = Date.now();
          filteredTasks = filteredTasks.filter(task => 
            task.dueDate && task.dueDate < now && task.completionPercentage < 100
          );
        }

        if (!filters.showCompleted) {
          filteredTasks = filteredTasks.filter(task => task.completionPercentage < 100);
        }

        // Apply date range filter
        if (filters.dateRange.start || filters.dateRange.end) {
          filteredTasks = filteredTasks.filter(task => {
            if (!task.dueDate) return false;
            
            const taskDate = new Date(task.dueDate);
            const start = filters.dateRange.start;
            const end = filters.dateRange.end;
            
            if (start && taskDate < start) return false;
            if (end && taskDate > end) return false;
            
            return true;
          });
        }

        return filteredTasks;
      },

      getTaskHierarchy: (taskId: string) => {
        const tasks = get().board.tasks;
        const hierarchy: EnhancedTodoItem[] = [];
        
        const addSubtasks = (parentId: string) => {
          Object.values(tasks).forEach(task => {
            if (task.parentId === parentId) {
              hierarchy.push(task);
              addSubtasks(task.id);
            }
          });
        };
        
        addSubtasks(taskId);
        return hierarchy;
      },

      calculateTaskProgress: (taskId: string) => {
        const task = get().board.tasks[taskId];
        if (!task) return 0;
        
        const subtasks = get().getTaskHierarchy(taskId);
        if (subtasks.length === 0) {
          return task.completionPercentage;
        }
        
        const totalProgress = subtasks.reduce((sum, subtask) => sum + subtask.completionPercentage, 0);
        return Math.round(totalProgress / subtasks.length);
      },

      // Column management operations
      addColumn: async (title: string, color: string = '#6366f1') => {
        try {
          const columnId = `column-${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          const newColumn: TodoColumn = {
            id: columnId,
            title,
            taskIds: [],
            color,
          };

          set((state) => ({
            board: {
              ...state.board,
              columns: {
                ...state.board.columns,
                [columnId]: newColumn,
              },
              columnOrder: [...state.board.columnOrder, columnId],
            },
          }));
        } catch (error: any) {
          console.error('Error adding column:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      updateColumn: async (columnId: string, title: string, color?: string) => {
        try {
          set((state) => {
            const column = state.board.columns[columnId];
            if (!column) return state;

            return {
              board: {
                ...state.board,
                columns: {
                  ...state.board.columns,
                  [columnId]: {
                    ...column,
                    title,
                    color: color || column.color,
                  },
                },
              },
            };
          });
        } catch (error: any) {
          console.error('Error updating column:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      deleteColumn: async (columnId: string) => {
        try {
          set((state) => {
            const { [columnId]: deletedColumn, ...remainingColumns } = state.board.columns;
            const newColumnOrder = state.board.columnOrder.filter(id => id !== columnId);

            // Move tasks from deleted column to the first remaining column
            const firstColumnId = newColumnOrder[0];
            if (firstColumnId && deletedColumn) {
              remainingColumns[firstColumnId] = {
                ...remainingColumns[firstColumnId],
                taskIds: [...remainingColumns[firstColumnId].taskIds, ...deletedColumn.taskIds],
              };
            }

            return {
              board: {
                ...state.board,
                columns: remainingColumns,
                columnOrder: newColumnOrder,
              },
            };
          });
        } catch (error: any) {
          console.error('Error deleting column:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      subscribeToUpdates: (userId: string) => {
        const subscription = subscribeToEnhancedTodos(userId, (payload) => {
          console.log('Real-time enhanced todo update:', payload);
          // Refetch todos when changes occur
          get().fetchTodos(userId);
        });

        return () => {
          subscription.unsubscribe();
        };
      },

      // Advanced search and analytics
      searchTasks: async (query: string, filters: any = {}) => {
        try {
          const state = get();
          const userId = state.board.tasks ? Object.values(state.board.tasks)[0]?.createdBy : '';
          if (!userId) return [];

          const results = await searchTasks(userId, query, filters);
          return results;
        } catch (error) {
          console.error('Error searching tasks:', error);
          return [];
        }
      },

      getAnalytics: async () => {
        try {
          const state = get();
          const userId = state.board.tasks ? Object.values(state.board.tasks)[0]?.createdBy : '';
          if (!userId) return null;

          const analytics = await getTaskAnalytics(userId);

          set((state) => ({
            analytics: {
              ...state.analytics,
              ...analytics,
            },
          }));

          return analytics;
        } catch (error) {
          console.error('Error getting analytics:', error);
          return null;
        }
      },

      // Export/Import functionality
      exportTasks: async (format: 'json' | 'csv' = 'json') => {
        try {
          const state = get();
          const userId = state.board.tasks ? Object.values(state.board.tasks)[0]?.createdBy : '';
          if (!userId) return '';

          return await exportTasks(userId, format);
        } catch (error) {
          console.error('Error exporting tasks:', error);
          return '';
        }
      },

      importTasks: async (data: string, format: 'json' | 'csv' = 'json') => {
        try {
          const state = get();
          const userId = state.board.tasks ? Object.values(state.board.tasks)[0]?.createdBy : '';
          if (!userId) return [];

          const importedTasks = await importTasks(userId, data, format);

          // Refresh tasks after import
          await get().fetchTodos(userId);

          return importedTasks;
        } catch (error) {
          console.error('Error importing tasks:', error);
          return [];
        }
      },

      reset: () => {
        set(initialEnhancedState);
      },
    })
);
